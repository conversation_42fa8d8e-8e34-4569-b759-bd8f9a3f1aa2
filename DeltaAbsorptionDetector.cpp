#include "sierrachart.h"

SCDLLName("Delta Absorption Detector")

/*==========================================================================*/
SCSFExport scsf_DeltaAbsorptionDetector(SCStudyInterfaceRef sc)
{
    // Subgraphs
    SCSubgraphRef BuySignal = sc.Subgraph[0];
    SCSubgraphRef SellSignal = sc.Subgraph[1];
    SCSubgraphRef DeltaLine = sc.Subgraph[2];
    SCSubgraphRef AbsorptionStrength = sc.Subgraph[3];
    SCSubgraphRef CumulativeDelta = sc.Subgraph[4];
    SCSubgraphRef DeltaEfficiency = sc.Subgraph[5];
    
    // Inputs
    SCInputRef DeltaThreshold = sc.Input[0];
    SCInputRef PriceMovementThreshold = sc.Input[1];
    SCInputRef LookbackPeriod = sc.Input[2];
    SCInputRef MinVolume = sc.Input[3];
    SCInputRef AbsorptionSensitivity = sc.Input[4];
    SCInputRef EnableAlerts = sc.Input[5];
    SCInputRef EnableBuySignals = sc.Input[6];
    SCInputRef EnableSellSignals = sc.Input[7];
    SCInputRef UseCumulativeDelta = sc.Input[8];
    SCInputRef DeltaEfficiencyThreshold = sc.Input[9];
    SCInputRef ResetCumulativeDelta = sc.Input[10];
    
    if (sc.SetDefaults)
    {
        sc.GraphName = "Delta Absorption Detector";
        sc.StudyDescription = "Detects absorption patterns using delta analysis. High buy delta with minimal price movement = Sell Signal. High sell delta with minimal price movement = Buy Signal.";
        
        sc.AutoLoop = 1;
        sc.GraphRegion = 1;
        sc.ValueFormat = 2;
        sc.ScaleRangeType = SCALE_INDEPENDENT;
        
        // Subgraph settings
        BuySignal.Name = "Buy Signal";
        BuySignal.DrawStyle = DRAWSTYLE_ARROW_UP;
        BuySignal.PrimaryColor = RGB(0, 255, 0); // Green
        BuySignal.LineWidth = 3;
        BuySignal.DrawZeros = false;
        
        SellSignal.Name = "Sell Signal";
        SellSignal.DrawStyle = DRAWSTYLE_ARROW_DOWN;
        SellSignal.PrimaryColor = RGB(255, 0, 0); // Red
        SellSignal.LineWidth = 3;
        SellSignal.DrawZeros = false;
        
        DeltaLine.Name = "Delta";
        DeltaLine.DrawStyle = DRAWSTYLE_LINE;
        DeltaLine.PrimaryColor = RGB(255, 255, 0); // Yellow
        DeltaLine.LineWidth = 1;
        DeltaLine.DrawZeros = true;
        
        AbsorptionStrength.Name = "Absorption Strength";
        AbsorptionStrength.DrawStyle = DRAWSTYLE_BAR;
        AbsorptionStrength.PrimaryColor = RGB(128, 128, 128); // Gray
        AbsorptionStrength.LineWidth = 2;
        AbsorptionStrength.DrawZeros = false;

        CumulativeDelta.Name = "Cumulative Delta";
        CumulativeDelta.DrawStyle = DRAWSTYLE_LINE;
        CumulativeDelta.PrimaryColor = RGB(255, 165, 0); // Orange
        CumulativeDelta.LineWidth = 1;
        CumulativeDelta.DrawZeros = true;

        DeltaEfficiency.Name = "Delta Efficiency";
        DeltaEfficiency.DrawStyle = DRAWSTYLE_LINE;
        DeltaEfficiency.PrimaryColor = RGB(255, 0, 255); // Magenta
        DeltaEfficiency.LineWidth = 1;
        DeltaEfficiency.DrawZeros = true;
        
        // Input settings
        DeltaThreshold.Name = "Delta Threshold (minimum delta to consider)";
        DeltaThreshold.SetFloat(100.0f);
        DeltaThreshold.SetFloatLimits(1.0f, 10000.0f);
        
        PriceMovementThreshold.Name = "Price Movement Threshold (ticks)";
        PriceMovementThreshold.SetFloat(2.0f);
        PriceMovementThreshold.SetFloatLimits(0.5f, 20.0f);
        
        LookbackPeriod.Name = "Lookback Period (bars)";
        LookbackPeriod.SetInt(5);
        LookbackPeriod.SetIntLimits(1, 50);
        
        MinVolume.Name = "Minimum Volume";
        MinVolume.SetFloat(50.0f);
        MinVolume.SetFloatLimits(1.0f, 10000.0f);
        
        AbsorptionSensitivity.Name = "Absorption Sensitivity (0.1-1.0)";
        AbsorptionSensitivity.SetFloat(0.7f);
        AbsorptionSensitivity.SetFloatLimits(0.1f, 1.0f);
        
        EnableAlerts.Name = "Enable Alerts";
        EnableAlerts.SetYesNo(1);
        
        EnableBuySignals.Name = "Enable Buy Signals";
        EnableBuySignals.SetYesNo(1);
        
        EnableSellSignals.Name = "Enable Sell Signals";
        EnableSellSignals.SetYesNo(1);

        UseCumulativeDelta.Name = "Use Cumulative Delta Analysis";
        UseCumulativeDelta.SetYesNo(0);

        DeltaEfficiencyThreshold.Name = "Delta Efficiency Threshold (%)";
        DeltaEfficiencyThreshold.SetFloat(30.0f);
        DeltaEfficiencyThreshold.SetFloatLimits(1.0f, 100.0f);

        ResetCumulativeDelta.Name = "Reset Cumulative Delta Daily";
        ResetCumulativeDelta.SetYesNo(1);

        return;
    }
    
    // Calculate delta for current bar
    float CurrentDelta = sc.AskVolume[sc.Index] - sc.BidVolume[sc.Index];
    DeltaLine[sc.Index] = CurrentDelta;

    // Calculate cumulative delta
    if (UseCumulativeDelta.GetYesNo())
    {
        sc.CumulativeDeltaVolume(sc.BaseDataIn, CumulativeDelta, sc.Index, ResetCumulativeDelta.GetYesNo());
    }

    // Calculate delta efficiency (delta as percentage of total volume)
    float TotalVolume = sc.Volume[sc.Index];
    float DeltaEfficiencyValue = 0.0f;
    if (TotalVolume > 0)
    {
        DeltaEfficiencyValue = (abs(CurrentDelta) / TotalVolume) * 100.0f;
    }
    DeltaEfficiency[sc.Index] = DeltaEfficiencyValue;

    // Skip if not enough data
    if (sc.Index < LookbackPeriod.GetInt())
        return;
    
    // Get current bar data
    float CurrentHigh = sc.High[sc.Index];
    float CurrentLow = sc.Low[sc.Index];
    float CurrentClose = sc.Close[sc.Index];
    float CurrentVolume = sc.Volume[sc.Index];
    
    // Skip if volume is too low
    if (CurrentVolume < MinVolume.GetFloat())
        return;
    
    // Calculate price movement in ticks
    float PriceRange = CurrentHigh - CurrentLow;
    float PriceRangeInTicks = PriceRange / sc.TickSize;
    
    // Calculate average delta and price movement over lookback period
    float TotalDelta = 0.0f;
    float TotalPriceMovement = 0.0f;
    float TotalVolume = 0.0f;
    
    for (int i = 0; i < LookbackPeriod.GetInt(); i++)
    {
        int BarIndex = sc.Index - i;
        if (BarIndex < 0) break;
        
        float BarDelta = sc.AskVolume[BarIndex] - sc.BidVolume[BarIndex];
        float BarPriceRange = (sc.High[BarIndex] - sc.Low[BarIndex]) / sc.TickSize;
        
        TotalDelta += abs(BarDelta);
        TotalPriceMovement += BarPriceRange;
        TotalVolume += sc.Volume[BarIndex];
    }
    
    float AvgDelta = TotalDelta / LookbackPeriod.GetInt();
    float AvgPriceMovement = TotalPriceMovement / LookbackPeriod.GetInt();
    
    // Skip if average delta is too low
    if (AvgDelta < DeltaThreshold.GetFloat())
        return;
    
    // Calculate expected price movement based on delta
    float DeltaRatio = abs(CurrentDelta) / AvgDelta;
    float ExpectedPriceMovement = AvgPriceMovement * DeltaRatio;
    
    // Calculate absorption strength
    float AbsorptionRatio = 0.0f;
    if (ExpectedPriceMovement > 0)
    {
        AbsorptionRatio = 1.0f - (PriceRangeInTicks / ExpectedPriceMovement);
        AbsorptionRatio = max(0.0f, min(1.0f, AbsorptionRatio)); // Clamp between 0 and 1
    }
    
    AbsorptionStrength[sc.Index] = AbsorptionRatio * 100.0f; // Convert to percentage
    
    // Check for absorption conditions
    bool HighDelta = abs(CurrentDelta) >= DeltaThreshold.GetFloat();
    bool LowPriceMovement = PriceRangeInTicks <= PriceMovementThreshold.GetFloat();
    bool SignificantAbsorption = AbsorptionRatio >= AbsorptionSensitivity.GetFloat();
    bool HighDeltaEfficiency = DeltaEfficiencyValue >= DeltaEfficiencyThreshold.GetFloat();

    // Additional filter: check for cumulative delta divergence if enabled
    bool CumulativeDeltaDivergence = true;
    if (UseCumulativeDelta.GetYesNo() && sc.Index > 0)
    {
        float PrevCumDelta = CumulativeDelta[sc.Index - 1];
        float CurrentCumDelta = CumulativeDelta[sc.Index];
        float CumDeltaChange = CurrentCumDelta - PrevCumDelta;

        // Check if cumulative delta direction matches current delta direction
        bool SameDirection = (CurrentDelta > 0 && CumDeltaChange > 0) || (CurrentDelta < 0 && CumDeltaChange < 0);
        CumulativeDeltaDivergence = !SameDirection; // We want divergence for absorption
    }
    
    // Main absorption detection logic
    bool AbsorptionDetected = HighDelta && LowPriceMovement && SignificantAbsorption && HighDeltaEfficiency;

    // Add cumulative delta filter if enabled
    if (UseCumulativeDelta.GetYesNo())
    {
        AbsorptionDetected = AbsorptionDetected && CumulativeDeltaDivergence;
    }

    if (AbsorptionDetected)
    {
        // High positive delta (buying pressure) but minimal price movement = Sell Signal
        if (CurrentDelta > 0 && EnableSellSignals.GetYesNo())
        {
            SellSignal[sc.Index] = CurrentHigh + (2 * sc.TickSize);

            if (EnableAlerts.GetYesNo())
            {
                SCString AlertMessage;
                AlertMessage.Format("SELL SIGNAL: High buy delta (%.0f) absorbed at %.2f. Absorption: %.1f%%, Efficiency: %.1f%%",
                    CurrentDelta, CurrentClose, AbsorptionRatio * 100.0f, DeltaEfficiencyValue);
                sc.SetAlert(1, sc.Index, AlertMessage);
            }
        }
        // High negative delta (selling pressure) but minimal price movement = Buy Signal
        else if (CurrentDelta < 0 && EnableBuySignals.GetYesNo())
        {
            BuySignal[sc.Index] = CurrentLow - (2 * sc.TickSize);

            if (EnableAlerts.GetYesNo())
            {
                SCString AlertMessage;
                AlertMessage.Format("BUY SIGNAL: High sell delta (%.0f) absorbed at %.2f. Absorption: %.1f%%, Efficiency: %.1f%%",
                    CurrentDelta, CurrentClose, AbsorptionRatio * 100.0f, DeltaEfficiencyValue);
                sc.SetAlert(2, sc.Index, AlertMessage);
            }
        }
    }
}
