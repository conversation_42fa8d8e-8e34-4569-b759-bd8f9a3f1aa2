#include "sierrachart.h"
#include "SCStudyFunctions.h"

SCDLLName("Absorption Detection")

/*==========================================================================*/
SCSFExport scsf_AbsorptionDetection(SCStudyInterfaceRef sc)
{
    // Subgraph declarations
    SCSubgraphRef Subgraph_AggressiveBuyVolume = sc.Subgraph[0];
    SCSubgraphRef Subgraph_AggressiveSellVolume = sc.Subgraph[1];
    SCSubgraphRef Subgraph_AbsorptionRatio = sc.Subgraph[2];
    SCSubgraphRef Subgraph_BuySignal = sc.Subgraph[3];
    SCSubgraphRef Subgraph_SellSignal = sc.Subgraph[4];
    SCSubgraphRef Subgraph_NetAggressive = sc.Subgraph[5];

    // Input declarations
    SCInputRef Input_MinAggressiveVolume = sc.Input[0];
    SCInputRef Input_AbsorptionThreshold = sc.Input[1];
    SCInputRef Input_LookbackPeriod = sc.Input[2];
    SCInputRef Input_PriceMovementSensitivity = sc.Input[3];
    SCInputRef Input_SignalSensitivity = sc.Input[4];
    SCInputRef Input_EnableDebugLog = sc.Input[5];

    if (sc.SetDefaults)
    {
        // Set the configuration and defaults
        sc.GraphName = "Absorption Detection";
        sc.StudyDescription = "Detects absorption patterns using aggressive order flow analysis. Sell signals when aggressive buys don't move price up, buy signals when aggressive sells don't move price down.";
        
        sc.AutoLoop = 1;
        sc.GraphRegion = 1;
        sc.ValueFormat = 0;
        sc.CalculationPrecedence = LOW_PREC_LEVEL;
        sc.FreeDLL = 1;

        // Subgraph configuration
        Subgraph_AggressiveBuyVolume.Name = "Aggressive Buy Volume";
        Subgraph_AggressiveBuyVolume.DrawStyle = DRAWSTYLE_BAR;
        Subgraph_AggressiveBuyVolume.PrimaryColor = RGB(0, 255, 0);
        Subgraph_AggressiveBuyVolume.DrawZeros = false;

        Subgraph_AggressiveSellVolume.Name = "Aggressive Sell Volume";
        Subgraph_AggressiveSellVolume.DrawStyle = DRAWSTYLE_BAR;
        Subgraph_AggressiveSellVolume.PrimaryColor = RGB(255, 0, 0);
        Subgraph_AggressiveSellVolume.DrawZeros = false;

        Subgraph_AbsorptionRatio.Name = "Absorption Ratio";
        Subgraph_AbsorptionRatio.DrawStyle = DRAWSTYLE_LINE;
        Subgraph_AbsorptionRatio.PrimaryColor = RGB(255, 255, 0);
        Subgraph_AbsorptionRatio.DrawZeros = false;

        Subgraph_BuySignal.Name = "Buy Signal";
        Subgraph_BuySignal.DrawStyle = DRAWSTYLE_ARROWUP;
        Subgraph_BuySignal.PrimaryColor = RGB(0, 255, 0);
        Subgraph_BuySignal.LineWidth = 3;
        Subgraph_BuySignal.DrawZeros = false;

        Subgraph_SellSignal.Name = "Sell Signal";
        Subgraph_SellSignal.DrawStyle = DRAWSTYLE_ARROWDOWN;
        Subgraph_SellSignal.PrimaryColor = RGB(255, 0, 0);
        Subgraph_SellSignal.LineWidth = 3;
        Subgraph_SellSignal.DrawZeros = false;

        Subgraph_NetAggressive.Name = "Net Aggressive Volume";
        Subgraph_NetAggressive.DrawStyle = DRAWSTYLE_LINE;
        Subgraph_NetAggressive.PrimaryColor = RGB(128, 128, 255);
        Subgraph_NetAggressive.DrawZeros = true;

        // Input configuration
        Input_MinAggressiveVolume.Name = "Minimum Aggressive Volume";
        Input_MinAggressiveVolume.SetInt(100);

        Input_AbsorptionThreshold.Name = "Absorption Threshold (0.0-1.0)";
        Input_AbsorptionThreshold.SetFloat(0.3f);

        Input_LookbackPeriod.Name = "Lookback Period (bars)";
        Input_LookbackPeriod.SetInt(5);

        Input_PriceMovementSensitivity.Name = "Price Movement Sensitivity";
        Input_PriceMovementSensitivity.SetFloat(1.0f);

        Input_SignalSensitivity.Name = "Signal Sensitivity (1-10)";
        Input_SignalSensitivity.SetInt(5);

        Input_EnableDebugLog.Name = "Enable Debug Logging";
        Input_EnableDebugLog.SetYesNo(0);

        return;
    }

    // Get input values
    const int MinAggressiveVolume = Input_MinAggressiveVolume.GetInt();
    const float AbsorptionThreshold = Input_AbsorptionThreshold.GetFloat();
    const int LookbackPeriod = max(1, Input_LookbackPeriod.GetInt());
    const float PriceMovementSensitivity = Input_PriceMovementSensitivity.GetFloat();
    const int SignalSensitivity = max(1, min(10, Input_SignalSensitivity.GetInt()));
    const bool EnableDebugLog = Input_EnableDebugLog.GetYesNo();

    // Get Time and Sales data
    c_SCTimeAndSalesArray TimeAndSalesArray;
    sc.GetTimeAndSales(TimeAndSalesArray);
    
    if (TimeAndSalesArray.Size() == 0)
        return;

    // Get time and sales indexes for current bar
    int TSBeginIndex = -1;
    int TSEndIndex = -1;
    sc.GetTimeSalesArrayIndexesForBarIndex(sc.Index, TSBeginIndex, TSEndIndex);
    
    if (TSBeginIndex == -1 || TSEndIndex == -1)
        return;

    // Initialize variables for vectorized calculations
    float AggressiveBuyVolume = 0.0f;
    float AggressiveSellVolume = 0.0f;
    float TotalVolume = 0.0f;

    // Vectorized processing of time and sales data
    for (int TSIndex = TSBeginIndex; TSIndex <= TSEndIndex; TSIndex++)
    {
        const s_TimeAndSales& TSRecord = TimeAndSalesArray[TSIndex];
        
        if (TSRecord.Volume == 0)
            continue;

        TotalVolume += TSRecord.Volume;

        // Classify aggressive orders by comparing trade price to bid/ask
        if (TSRecord.Bid > 0 && TSRecord.Ask > 0)
        {
            float MidPrice = (TSRecord.Bid + TSRecord.Ask) * 0.5f;
            
            // Aggressive buy: trade at or above mid price (closer to ask)
            if (TSRecord.Price >= MidPrice)
            {
                AggressiveBuyVolume += TSRecord.Volume;
            }
            // Aggressive sell: trade below mid price (closer to bid)
            else
            {
                AggressiveSellVolume += TSRecord.Volume;
            }
        }
        else if (TSRecord.Price > 0)
        {
            // Fallback: use price comparison with previous bar close
            if (sc.Index > 0)
            {
                float PrevClose = sc.Close[sc.Index - 1];
                if (TSRecord.Price >= PrevClose)
                {
                    AggressiveBuyVolume += TSRecord.Volume;
                }
                else
                {
                    AggressiveSellVolume += TSRecord.Volume;
                }
            }
        }
    }

    // Store aggressive volume data
    Subgraph_AggressiveBuyVolume[sc.Index] = AggressiveBuyVolume;
    Subgraph_AggressiveSellVolume[sc.Index] = AggressiveSellVolume;
    Subgraph_NetAggressive[sc.Index] = AggressiveBuyVolume - AggressiveSellVolume;

    // Calculate price movement for current bar
    float PriceRange = sc.High[sc.Index] - sc.Low[sc.Index];
    float PriceChange = sc.Close[sc.Index] - sc.Open[sc.Index];
    
    if (PriceRange <= 0)
        return;

    // Calculate expected price movement based on aggressive volume imbalance
    float NetAggressiveVolume = AggressiveBuyVolume - AggressiveSellVolume;
    float ExpectedMovement = 0.0f;
    
    if (TotalVolume > 0)
    {
        float VolumeImbalanceRatio = NetAggressiveVolume / TotalVolume;
        ExpectedMovement = VolumeImbalanceRatio * PriceRange * PriceMovementSensitivity;
    }

    // Calculate absorption ratio (actual movement vs expected movement)
    float AbsorptionRatio = 0.0f;
    if (abs(ExpectedMovement) > 0)
    {
        AbsorptionRatio = abs(PriceChange) / abs(ExpectedMovement);
        AbsorptionRatio = min(AbsorptionRatio, 2.0f); // Cap at 2.0 for display
    }

    Subgraph_AbsorptionRatio[sc.Index] = AbsorptionRatio;

    // Generate signals based on absorption detection
    bool BuySignal = false;
    bool SellSignal = false;

    // Check for sufficient volume and absorption conditions
    if (AggressiveBuyVolume >= MinAggressiveVolume && AbsorptionRatio <= AbsorptionThreshold)
    {
        // Sell absorption: lots of aggressive buys but price didn't move up
        if (NetAggressiveVolume > 0 && PriceChange <= 0)
        {
            SellSignal = true;
        }
    }

    if (AggressiveSellVolume >= MinAggressiveVolume && AbsorptionRatio <= AbsorptionThreshold)
    {
        // Buy absorption: lots of aggressive sells but price didn't move down
        if (NetAggressiveVolume < 0 && PriceChange >= 0)
        {
            BuySignal = true;
        }
    }

    // Apply signal sensitivity filter using lookback period
    if (BuySignal || SellSignal)
    {
        int RecentSignalCount = 0;
        int StartIndex = max(0, sc.Index - LookbackPeriod);
        
        // Vectorized count of recent signals
        for (int i = StartIndex; i < sc.Index; i++)
        {
            if (Subgraph_BuySignal[i] != 0 || Subgraph_SellSignal[i] != 0)
            {
                RecentSignalCount++;
            }
        }

        // Reduce signal frequency based on sensitivity setting
        int MaxRecentSignals = (10 - SignalSensitivity) / 2;
        if (RecentSignalCount >= MaxRecentSignals)
        {
            BuySignal = false;
            SellSignal = false;
        }
    }

    // Set signal outputs
    if (BuySignal)
    {
        Subgraph_BuySignal[sc.Index] = sc.Low[sc.Index] - sc.TickSize * 2;
        
        if (EnableDebugLog)
        {
            SCString LogMessage;
            LogMessage.Format("Buy Signal: Bar=%d, AggSell=%.0f, AbsRatio=%.3f, PriceChange=%.4f", 
                sc.Index, AggressiveSellVolume, AbsorptionRatio, PriceChange);
            sc.AddMessageToLog(LogMessage, 0);
        }
    }

    if (SellSignal)
    {
        Subgraph_SellSignal[sc.Index] = sc.High[sc.Index] + sc.TickSize * 2;
        
        if (EnableDebugLog)
        {
            SCString LogMessage;
            LogMessage.Format("Sell Signal: Bar=%d, AggBuy=%.0f, AbsRatio=%.3f, PriceChange=%.4f", 
                sc.Index, AggressiveBuyVolume, AbsorptionRatio, PriceChange);
            sc.AddMessageToLog(LogMessage, 0);
        }
    }
}
