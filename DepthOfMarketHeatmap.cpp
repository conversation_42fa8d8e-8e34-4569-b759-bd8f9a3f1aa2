#include "sierrachart.h"

SCDLLName("Depth of Market Heatmap")

/*==========================================================================*/

// Main study function - EFFICIENT VERSION
SCSFExport scsf_DepthOfMarketHeatmap(SCStudyInterfaceRef sc)
{
    // Input references
    SCInputRef Input_SmoothingPeriod = sc.Input[0];
    SCInputRef Input_DepthLevels = sc.Input[1];
    SCInputRef Input_MinVolumeThreshold = sc.Input[2];

    if (sc.SetDefaults)
    {
        // Set the configuration and defaults
        sc.GraphName = "Depth of Market Heatmap";
        sc.StudyDescription = "Efficient DOM heatmap with Bookmap-style smoothing";
        sc.AutoLoop = 1;
        sc.GraphRegion = 0;
        sc.CalculationPrecedence = LOW_PREC_LEVEL;
        sc.UsesMarketDepthData = 1;

        // Create multiple subgraphs for different price levels
        // Each subgraph represents a price level with bid/ask data
        for (int i = 0; i < 40; i++) // 40 price levels (20 above, 20 below current price)
        {
            sc.Subgraph[i].Name.Format("Level %d", i - 20);
            sc.Subgraph[i].DrawStyle = DRAWSTYLE_COLOR_BAR_HOLLOW;
            sc.Subgraph[i].PrimaryColor = RGB(100, 100, 100);
            sc.Subgraph[i].DrawZeros = 0;
            sc.Subgraph[i].AutoColoring = AUTOCOLOR_GRADIENT;
        }

        // Input settings - simplified for efficiency
        Input_SmoothingPeriod.Name = "Smoothing Period";
        Input_SmoothingPeriod.SetInt(10);
        Input_SmoothingPeriod.SetIntLimits(1, 50);

        Input_DepthLevels.Name = "Depth Levels";
        Input_DepthLevels.SetInt(20);
        Input_DepthLevels.SetIntLimits(5, 20);

        Input_MinVolumeThreshold.Name = "Min Volume";
        Input_MinVolumeThreshold.SetInt(10);
        Input_MinVolumeThreshold.SetIntLimits(1, 1000);

        return;
    }

    // EFFICIENT BACKEND PROCESSING - No frontend calculations!

    // Only process on new bars or when settings change
    if (sc.Index < sc.ArraySize - 1 && !sc.GetBarHasClosedStatus(sc.Index))
        return;

    // Get current market depth data efficiently
    int BidLevels = sc.GetBidMarketDepthNumberOfLevels();
    int AskLevels = sc.GetAskMarketDepthNumberOfLevels();

    if (BidLevels == 0 && AskLevels == 0)
        return;

    int DepthLevels = Input_DepthLevels.GetInt();
    int SmoothingPeriod = Input_SmoothingPeriod.GetInt();
    int MinVolume = Input_MinVolumeThreshold.GetInt();

    // Get current price for centering
    float CurrentPrice = sc.Close[sc.Index];

    // Process bid side efficiently
    for (int level = 0; level < DepthLevels && level < BidLevels; level++)
    {
        s_MarketDepthEntry BidEntry;
        if (sc.GetBidMarketDepthEntryAtLevel(BidEntry, level))
        {
            if (BidEntry.Quantity >= MinVolume)
            {
                // Apply simple smoothing using subgraph arrays
                float SmoothedValue = BidEntry.Quantity;
                if (SmoothingPeriod > 1 && sc.Index > 0)
                {
                    float Alpha = 2.0f / (SmoothingPeriod + 1.0f);
                    SmoothedValue = Alpha * BidEntry.Quantity +
                                   (1.0f - Alpha) * sc.Subgraph[level][sc.Index - 1];
                }

                // Store in subgraph - this is the efficient way!
                sc.Subgraph[level][sc.Index] = SmoothedValue;

                // Set color based on intensity
                float Intensity = SmoothedValue / 1000.0f; // Normalize
                if (Intensity > 1.0f) Intensity = 1.0f;

                COLORREF BidColor = RGB(0, (int)(255 * Intensity), 0);
                sc.Subgraph[level].DataColor[sc.Index] = BidColor;
            }
        }
    }

    // Process ask side efficiently
    for (int level = 0; level < DepthLevels && level < AskLevels; level++)
    {
        s_MarketDepthEntry AskEntry;
        if (sc.GetAskMarketDepthEntryAtLevel(AskEntry, level))
        {
            if (AskEntry.Quantity >= MinVolume)
            {
                int SubgraphIndex = DepthLevels + level; // Offset for ask side

                // Apply simple smoothing
                float SmoothedValue = AskEntry.Quantity;
                if (SmoothingPeriod > 1 && sc.Index > 0)
                {
                    float Alpha = 2.0f / (SmoothingPeriod + 1.0f);
                    SmoothedValue = Alpha * AskEntry.Quantity +
                                   (1.0f - Alpha) * sc.Subgraph[SubgraphIndex][sc.Index - 1];
                }

                // Store in subgraph
                sc.Subgraph[SubgraphIndex][sc.Index] = SmoothedValue;

                // Set color based on intensity
                float Intensity = SmoothedValue / 1000.0f; // Normalize
                if (Intensity > 1.0f) Intensity = 1.0f;

                COLORREF AskColor = RGB((int)(255 * Intensity), 0, 0);
                sc.Subgraph[SubgraphIndex].DataColor[sc.Index] = AskColor;
            }
        }
    }
}


