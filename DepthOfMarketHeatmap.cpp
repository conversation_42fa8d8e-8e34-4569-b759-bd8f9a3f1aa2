#include "sierrachart.h"

SCDLLName("Simple DOM Heatmap")

/*==========================================================================*/
// SIMPLE, SAFE VERSION - No crashes!
SCSFExport scsf_DepthOfMarketHeatmap(SCStudyInterfaceRef sc)
{
    SCInputRef Input_SmoothingPeriod = sc.Input[0];

    if (sc.SetDefaults)
    {
        sc.GraphName = "Simple DOM Heatmap";
        sc.StudyDescription = "Basic DOM visualization - safe version";
        sc.AutoLoop = 1;
        sc.GraphRegion = 0;
        sc.UsesMarketDepthData = 1;

        // Just 2 simple subgraphs - bid and ask
        sc.Subgraph[0].Name = "Bid Volume";
        sc.Subgraph[0].DrawStyle = DRAWSTYLE_BAR;
        sc.Subgraph[0].PrimaryColor = RGB(0, 255, 0);
        sc.Subgraph[0].DrawZeros = 0;

        sc.Subgraph[1].Name = "Ask Volume";
        sc.Subgraph[1].DrawStyle = DRAWSTYLE_BAR;
        sc.Subgraph[1].PrimaryColor = RGB(255, 0, 0);
        sc.Subgraph[1].DrawZeros = 0;

        Input_SmoothingPeriod.Name = "Smoothing";
        Input_SmoothingPeriod.SetInt(5);
        Input_SmoothingPeriod.SetIntLimits(1, 20);

        return;
    }

    // Get raw bid/ask data
    s_MarketDepthEntry BidEntry, AskEntry;

    // Create temporary arrays for raw data
    SCFloatArrayRef RawBidData = sc.Subgraph[0].Arrays[0];
    SCFloatArrayRef RawAskData = sc.Subgraph[1].Arrays[0];

    // Get current values
    float BidVol = 0;
    float AskVol = 0;

    if (sc.GetBidMarketDepthEntryAtLevel(BidEntry, 0))
        BidVol = static_cast<float>(BidEntry.Quantity);

    if (sc.GetAskMarketDepthEntryAtLevel(AskEntry, 0))
        AskVol = static_cast<float>(AskEntry.Quantity);

    // Store raw values
    RawBidData[sc.Index] = BidVol;
    RawAskData[sc.Index] = AskVol;

    // Use SierraChart's vectorized exponential moving average
    int SmoothPeriod = Input_SmoothingPeriod.GetInt();

    // Vectorized smoothing - no loops!
    sc.ExponentialMovingAverage(RawBidData, sc.Subgraph[0], SmoothPeriod);
    sc.ExponentialMovingAverage(RawAskData, sc.Subgraph[1], SmoothPeriod);
}


