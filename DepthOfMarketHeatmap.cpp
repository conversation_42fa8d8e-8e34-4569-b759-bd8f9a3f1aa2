#include "sierrachart.h"

SCDLLName("Simple DOM Heatmap")

/*==========================================================================*/
// SIMPLE, SAFE VERSION - No crashes!
SCSFExport scsf_DepthOfMarketHeatmap(SCStudyInterfaceRef sc)
{
    SCInputRef Input_SmoothingPeriod = sc.Input[0];

    if (sc.SetDefaults)
    {
        sc.GraphName = "Simple DOM Heatmap";
        sc.StudyDescription = "Basic DOM visualization - safe version";
        sc.AutoLoop = 1;
        sc.GraphRegion = 0;
        sc.UsesMarketDepthData = 1;

        // Just 2 simple subgraphs - bid and ask
        sc.Subgraph[0].Name = "Bid Volume";
        sc.Subgraph[0].DrawStyle = DRAWSTYLE_BAR;
        sc.Subgraph[0].PrimaryColor = RGB(0, 255, 0);
        sc.Subgraph[0].DrawZeros = 0;

        sc.Subgraph[1].Name = "Ask Volume";
        sc.Subgraph[1].DrawStyle = DRAWSTYLE_BAR;
        sc.Subgraph[1].PrimaryColor = RGB(255, 0, 0);
        sc.Subgraph[1].DrawZeros = 0;

        Input_SmoothingPeriod.Name = "Smoothing";
        Input_SmoothingPeriod.SetInt(5);
        Input_SmoothingPeriod.SetIntLimits(1, 20);

        return;
    }

    // Simple processing - just get top level bid/ask
    s_MarketDepthEntry BidEntry, AskEntry;

    float BidVol = 0;
    float AskVol = 0;

    if (sc.GetBidMarketDepthEntryAtLevel(BidEntry, 0))
        BidVol = static_cast<float>(BidEntry.Quantity);

    if (sc.GetAskMarketDepthEntryAtLevel(AskEntry, 0))
        AskVol = static_cast<float>(AskEntry.Quantity);

    // Simple smoothing
    int SmoothPeriod = Input_SmoothingPeriod.GetInt();
    if (SmoothPeriod > 1 && sc.Index > 0)
    {
        float Alpha = 2.0f / (SmoothPeriod + 1.0f);
        BidVol = Alpha * BidVol + (1.0f - Alpha) * sc.Subgraph[0][sc.Index - 1];
        AskVol = Alpha * AskVol + (1.0f - Alpha) * sc.Subgraph[1][sc.Index - 1];
    }

    // Store values
    sc.Subgraph[0][sc.Index] = BidVol;
    sc.Subgraph[1][sc.Index] = AskVol;
}


