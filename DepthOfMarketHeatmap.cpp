#include "sierrachart.h"
#include <vector>
#include <algorithm>
#include <cmath>

SCDLLName("Depth of Market Heatmap")

/*==========================================================================*/
// Structure to hold depth data for heatmap visualization
struct s_DepthHeatmapData
{
    float Price = 0.0f;
    int BidQuantity = 0;
    int AskQuantity = 0;
    int MaxQuantity = 0;
    float SmoothedBidQuantity = 0.0f;
    float SmoothedAskQuantity = 0.0f;
    float SmoothedMaxQuantity = 0.0f;
    int AccumulatedBidVolume = 0;
    int AccumulatedAskVolume = 0;
    int TotalAccumulatedVolume = 0;

    s_DepthHeatmapData() {}

    s_DepthHeatmapData(float price, int bidQty, int askQty)
        : Price(price), BidQuantity(bidQty), AskQuantity(askQty)
    {
        MaxQuantity = max(bidQty, askQty);
        AccumulatedBidVolume = bidQty;
        AccumulatedAskVolume = askQty;
        TotalAccumulatedVolume = bidQty + askQty;
    }
};

/*==========================================================================*/
// Color interpolation function for heatmap
COLORREF InterpolateColor(COLORREF Color1, COLORREF Color2, float Factor)
{
    if (Factor <= 0.0f) return Color1;
    if (Factor >= 1.0f) return Color2;
    
    int R1 = GetRValue(Color1);
    int G1 = GetGValue(Color1);
    int B1 = GetBValue(Color1);
    
    int R2 = GetRValue(Color2);
    int G2 = GetGValue(Color2);
    int B2 = GetBValue(Color2);
    
    int R = (int)(R1 + (R2 - R1) * Factor);
    int G = (int)(G1 + (G2 - G1) * Factor);
    int B = (int)(B1 + (B2 - B1) * Factor);
    
    return RGB(R, G, B);
}

/*==========================================================================*/
// Function declarations
void DrawHeatmap(SCStudyInterfaceRef sc, const std::vector<s_DepthHeatmapData>& DepthData,
                float MaxQuantity, float Intensity, bool ShowBids, bool ShowAsks,
                COLORREF BidColor, COLORREF AskColor, COLORREF BackgroundColor,
                bool ShowVolumeNumbers, float Transparency, int MinVolumeThreshold);

/*==========================================================================*/
// Apply exponential moving average smoothing to depth data
void ApplySmoothing(std::vector<s_DepthHeatmapData>& DepthData, float SmoothingFactor)
{
    if (DepthData.empty() || SmoothingFactor <= 0.0f)
        return;
        
    // Initialize first values
    if (!DepthData.empty())
    {
        DepthData[0].SmoothedBidQuantity = static_cast<float>(DepthData[0].BidQuantity);
        DepthData[0].SmoothedAskQuantity = static_cast<float>(DepthData[0].AskQuantity);
        DepthData[0].SmoothedMaxQuantity = static_cast<float>(DepthData[0].MaxQuantity);
    }
    
    // Apply exponential smoothing
    for (size_t i = 1; i < DepthData.size(); ++i)
    {
        float Alpha = 2.0f / (SmoothingFactor + 1.0f);
        
        DepthData[i].SmoothedBidQuantity = Alpha * DepthData[i].BidQuantity + 
                                          (1.0f - Alpha) * DepthData[i-1].SmoothedBidQuantity;
        DepthData[i].SmoothedAskQuantity = Alpha * DepthData[i].AskQuantity + 
                                          (1.0f - Alpha) * DepthData[i-1].SmoothedAskQuantity;
        DepthData[i].SmoothedMaxQuantity = max(DepthData[i].SmoothedBidQuantity, 
                                              DepthData[i].SmoothedAskQuantity);
    }
}

/*==========================================================================*/
// Main study function
SCSFExport scsf_DepthOfMarketHeatmap(SCStudyInterfaceRef sc)
{
    // Input references
    SCInputRef Input_SmoothingPeriod = sc.Input[0];
    SCInputRef Input_DepthLevels = sc.Input[1];
    SCInputRef Input_HeatmapIntensity = sc.Input[2];
    SCInputRef Input_ShowBidSide = sc.Input[3];
    SCInputRef Input_ShowAskSide = sc.Input[4];
    SCInputRef Input_BidColor = sc.Input[5];
    SCInputRef Input_AskColor = sc.Input[6];
    SCInputRef Input_BackgroundColor = sc.Input[7];
    SCInputRef Input_UpdateFrequency = sc.Input[8];
    SCInputRef Input_PriceRange = sc.Input[9];
    SCInputRef Input_ShowVolumeNumbers = sc.Input[10];
    SCInputRef Input_VolumeAccumulation = sc.Input[11];
    SCInputRef Input_MinVolumeThreshold = sc.Input[12];
    SCInputRef Input_HeatmapTransparency = sc.Input[13];

    if (sc.SetDefaults)
    {
        // Set the configuration and defaults
        sc.GraphName = "Depth of Market Heatmap";
        sc.StudyDescription = "Displays market depth as a heatmap visualization similar to Bookmap with smoothing options";
        sc.AutoLoop = 0;
        sc.GraphRegion = 0;
        sc.CalculationPrecedence = LOW_PREC_LEVEL;
        sc.UsesMarketDepthData = 1; // Enable market depth data
        sc.MaintainVolumeAtPriceData = 1;
        
        // Input settings
        Input_SmoothingPeriod.Name = "Smoothing Period";
        Input_SmoothingPeriod.SetInt(10);
        Input_SmoothingPeriod.SetIntLimits(1, 100);
        Input_SmoothingPeriod.SetDescription("Period for exponential smoothing (1 = no smoothing, higher = more smoothing)");
        
        Input_DepthLevels.Name = "Depth Levels to Display";
        Input_DepthLevels.SetInt(20);
        Input_DepthLevels.SetIntLimits(5, 50);
        Input_DepthLevels.SetDescription("Number of price levels above and below current price to display");
        
        Input_HeatmapIntensity.Name = "Heatmap Intensity";
        Input_HeatmapIntensity.SetFloat(1.0f);
        Input_HeatmapIntensity.SetFloatLimits(0.1f, 5.0f);
        Input_HeatmapIntensity.SetDescription("Intensity multiplier for heatmap colors");
        
        Input_ShowBidSide.Name = "Show Bid Side";
        Input_ShowBidSide.SetYesNo(1);
        Input_ShowBidSide.SetDescription("Display bid side depth data");
        
        Input_ShowAskSide.Name = "Show Ask Side";
        Input_ShowAskSide.SetYesNo(1);
        Input_ShowAskSide.SetDescription("Display ask side depth data");
        
        Input_BidColor.Name = "Bid Color";
        Input_BidColor.SetColor(RGB(0, 255, 0)); // Green for bids
        Input_BidColor.SetDescription("Color for bid side heatmap");
        
        Input_AskColor.Name = "Ask Color";
        Input_AskColor.SetColor(RGB(255, 0, 0)); // Red for asks
        Input_AskColor.SetDescription("Color for ask side heatmap");
        
        Input_BackgroundColor.Name = "Background Color";
        Input_BackgroundColor.SetColor(RGB(0, 0, 0)); // Black background
        Input_BackgroundColor.SetDescription("Background color for heatmap");
        
        Input_UpdateFrequency.Name = "Update Frequency (ms)";
        Input_UpdateFrequency.SetInt(100);
        Input_UpdateFrequency.SetIntLimits(50, 1000);
        Input_UpdateFrequency.SetDescription("Update frequency in milliseconds");
        
        Input_PriceRange.Name = "Price Range (ticks)";
        Input_PriceRange.SetInt(50);
        Input_PriceRange.SetIntLimits(10, 200);
        Input_PriceRange.SetDescription("Price range in ticks around current price");

        Input_ShowVolumeNumbers.Name = "Show Volume Numbers";
        Input_ShowVolumeNumbers.SetYesNo(1);
        Input_ShowVolumeNumbers.SetDescription("Display volume numbers on heatmap");

        Input_VolumeAccumulation.Name = "Volume Accumulation";
        Input_VolumeAccumulation.SetYesNo(1);
        Input_VolumeAccumulation.SetDescription("Accumulate volume over time like Bookmap");

        Input_MinVolumeThreshold.Name = "Min Volume Threshold";
        Input_MinVolumeThreshold.SetInt(10);
        Input_MinVolumeThreshold.SetIntLimits(1, 1000);
        Input_MinVolumeThreshold.SetDescription("Minimum volume to display in heatmap");

        Input_HeatmapTransparency.Name = "Heatmap Transparency";
        Input_HeatmapTransparency.SetFloat(0.7f);
        Input_HeatmapTransparency.SetFloatLimits(0.1f, 1.0f);
        Input_HeatmapTransparency.SetDescription("Transparency level for heatmap (0.1 = very transparent, 1.0 = opaque)");

        return;
    }
    
    // Get market depth data
    c_ACSILDepthBars* DepthBars = sc.GetMarketDepthBars();
    if (DepthBars == nullptr)
    {
        sc.AddMessageToLog("Market depth data not available", 1);
        return;
    }

    // Get current market data
    float CurrentPrice = sc.Close[sc.ArraySize - 1];
    if (CurrentPrice <= 0.0f)
        return;

    float TickSize = sc.TickSize;
    if (TickSize <= 0.0f)
        TickSize = 0.01f; // Default tick size

    int PriceRange = Input_PriceRange.GetInt();
    int DepthLevels = Input_DepthLevels.GetInt();
    float SmoothingPeriod = static_cast<float>(Input_SmoothingPeriod.GetInt());
    float HeatmapIntensity = Input_HeatmapIntensity.GetFloat();

    // Calculate price levels to display
    float StartPrice = CurrentPrice - (PriceRange * TickSize);
    float EndPrice = CurrentPrice + (PriceRange * TickSize);

    // Collect depth data for current bar
    std::vector<s_DepthHeatmapData> DepthData;
    int CurrentBarIndex = sc.ArraySize - 1;

    if (CurrentBarIndex < 0 || !DepthBars->DepthDataExistsAt(CurrentBarIndex))
    {
        return; // No depth data available for current bar
    }

    // Get price tick indices for the range
    int StartTickIndex = DepthBars->PriceToTickIndex(StartPrice);
    int EndTickIndex = DepthBars->PriceToTickIndex(EndPrice);

    // Collect depth data for each price level
    for (int TickIndex = StartTickIndex; TickIndex <= EndTickIndex; ++TickIndex)
    {
        float Price = DepthBars->TickIndexToPrice(TickIndex);
        int BidQuantity = DepthBars->GetMaxBidQuantity(CurrentBarIndex, TickIndex);
        int AskQuantity = DepthBars->GetMaxAskQuantity(CurrentBarIndex, TickIndex);

        if (BidQuantity > 0 || AskQuantity > 0)
        {
            DepthData.push_back(s_DepthHeatmapData(Price, BidQuantity, AskQuantity));
        }
    }

    // Apply smoothing if enabled
    if (SmoothingPeriod > 1.0f)
    {
        ApplySmoothing(DepthData, SmoothingPeriod);
    }
    else
    {
        // No smoothing - copy raw values to smoothed values
        for (auto& data : DepthData)
        {
            data.SmoothedBidQuantity = static_cast<float>(data.BidQuantity);
            data.SmoothedAskQuantity = static_cast<float>(data.AskQuantity);
            data.SmoothedMaxQuantity = static_cast<float>(data.MaxQuantity);
        }
    }

    // Find maximum quantity for normalization
    float MaxQuantityInRange = 0.0f;
    for (const auto& data : DepthData)
    {
        MaxQuantityInRange = max(MaxQuantityInRange, data.SmoothedMaxQuantity);
    }

    if (MaxQuantityInRange <= 0.0f)
        return; // No data to display

    // Draw the heatmap
    DrawHeatmap(sc, DepthData, MaxQuantityInRange, HeatmapIntensity,
                Input_ShowBidSide.GetYesNo(), Input_ShowAskSide.GetYesNo(),
                Input_BidColor.GetColor(), Input_AskColor.GetColor(),
                Input_BackgroundColor.GetColor(), Input_ShowVolumeNumbers.GetYesNo(),
                Input_HeatmapTransparency.GetFloat(), Input_MinVolumeThreshold.GetInt());
}

/*==========================================================================*/
// Function to draw the heatmap visualization
void DrawHeatmap(SCStudyInterfaceRef sc, const std::vector<s_DepthHeatmapData>& DepthData,
                float MaxQuantity, float Intensity, bool ShowBids, bool ShowAsks,
                COLORREF BidColor, COLORREF AskColor, COLORREF BackgroundColor,
                bool ShowVolumeNumbers, float Transparency, int MinVolumeThreshold)
{
    if (DepthData.empty() || MaxQuantity <= 0.0f)
        return;

    // Get chart dimensions and current bar position
    int CurrentBarIndex = sc.ArraySize - 1;
    int BarXCoordinate = sc.BarIndexToXPixelCoordinate(CurrentBarIndex);

    // Calculate heatmap dimensions
    int HeatmapWidth = 50; // Width in pixels for each side
    int PriceHeight = 3;   // Height per price level in pixels

    // Draw background
    n_ACSIL::s_GraphicsRectangle BackgroundRect;
    BackgroundRect.Left = BarXCoordinate - HeatmapWidth;
    BackgroundRect.Right = BarXCoordinate + HeatmapWidth;
    BackgroundRect.Top = 0;
    BackgroundRect.Bottom = static_cast<int>(DepthData.size() * PriceHeight);

    n_ACSIL::s_GraphicsBrush BackgroundBrush;
    BackgroundBrush.Color.SetColorValue(BackgroundColor);
    sc.p_GDIFunction->SetBrush(BackgroundBrush);
    sc.p_GDIFunction->FillRectangle(BackgroundRect);

    // Draw depth levels
    for (size_t i = 0; i < DepthData.size(); ++i)
    {
        const s_DepthHeatmapData& data = DepthData[i];

        int YCoordinate = sc.PriceValueToYPixelCoordinate(data.Price, 0);

        // Draw bid side (left side)
        if (ShowBids && data.SmoothedBidQuantity >= MinVolumeThreshold)
        {
            float BidIntensity = (data.SmoothedBidQuantity / MaxQuantity) * Intensity * Transparency;
            BidIntensity = min(BidIntensity, Transparency);

            COLORREF BidHeatColor = InterpolateColor(BackgroundColor, BidColor, BidIntensity);

            n_ACSIL::s_GraphicsRectangle BidRect;
            BidRect.Left = BarXCoordinate - HeatmapWidth;
            BidRect.Right = BarXCoordinate;
            BidRect.Top = YCoordinate - PriceHeight / 2;
            BidRect.Bottom = YCoordinate + PriceHeight / 2;

            n_ACSIL::s_GraphicsBrush BidBrush;
            BidBrush.Color.SetColorValue(BidHeatColor);
            sc.p_GDIFunction->SetBrush(BidBrush);
            sc.p_GDIFunction->FillRectangle(BidRect);

            // Draw volume numbers if enabled
            if (ShowVolumeNumbers && data.SmoothedBidQuantity >= MinVolumeThreshold)
            {
                SCString VolumeText;
                VolumeText.Format("%.0f", data.SmoothedBidQuantity);

                n_ACSIL::s_GraphicsPoint TextPoint;
                TextPoint.X = BarXCoordinate - HeatmapWidth / 2;
                TextPoint.Y = YCoordinate;

                n_ACSIL::s_GraphicsColor TextColor;
                TextColor.SetColorValue(RGB(255, 255, 255)); // White text
                sc.p_GDIFunction->SetTextColor(TextColor);
                sc.p_GDIFunction->DrawText(VolumeText, TextPoint);
            }
        }

        // Draw ask side (right side)
        if (ShowAsks && data.SmoothedAskQuantity >= MinVolumeThreshold)
        {
            float AskIntensity = (data.SmoothedAskQuantity / MaxQuantity) * Intensity * Transparency;
            AskIntensity = min(AskIntensity, Transparency);

            COLORREF AskHeatColor = InterpolateColor(BackgroundColor, AskColor, AskIntensity);

            n_ACSIL::s_GraphicsRectangle AskRect;
            AskRect.Left = BarXCoordinate;
            AskRect.Right = BarXCoordinate + HeatmapWidth;
            AskRect.Top = YCoordinate - PriceHeight / 2;
            AskRect.Bottom = YCoordinate + PriceHeight / 2;

            n_ACSIL::s_GraphicsBrush AskBrush;
            AskBrush.Color.SetColorValue(AskHeatColor);
            sc.p_GDIFunction->SetBrush(AskBrush);
            sc.p_GDIFunction->FillRectangle(AskRect);

            // Draw volume numbers if enabled
            if (ShowVolumeNumbers && data.SmoothedAskQuantity >= MinVolumeThreshold)
            {
                SCString VolumeText;
                VolumeText.Format("%.0f", data.SmoothedAskQuantity);

                n_ACSIL::s_GraphicsPoint TextPoint;
                TextPoint.X = BarXCoordinate + HeatmapWidth / 2;
                TextPoint.Y = YCoordinate;

                n_ACSIL::s_GraphicsColor TextColor;
                TextColor.SetColorValue(RGB(255, 255, 255)); // White text
                sc.p_GDIFunction->SetTextColor(TextColor);
                sc.p_GDIFunction->DrawText(VolumeText, TextPoint);
            }
        }
    }
}
