# Compilation Guide for Depth of Market Heatmap

## Quick Start

Your `DepthOfMarketHeatmap.cpp` file is ready to compile! Here's how to get it working in SierraChart:

### Step 1: Copy the File
1. Copy `DepthOfMarketHeatmap.cpp` to your SierraChart `ACS_Source` folder
   - Usually located at: `C:\SierraChart\ACS_Source\`

### Step 2: Compile in SierraChart
1. Open SierraChart
2. Go to **Analysis** → **Build Advanced Custom Study DLL**
3. Select `DepthOfMarketHeatmap.cpp` from the list
4. Click **Build**
5. Wait for compilation to complete (should show "Build successful")

### Step 3: Add to Chart
1. Open a chart with market depth data
2. Go to **Analysis** → **Studies**
3. Click **Add Custom Study**
4. Find "Depth of Market Heatmap" in the list
5. Click **OK**

### Step 4: Configure Settings
The study will appear with these key settings:

**Essential Settings:**
- **Smoothing Period**: Start with 10-15 for Bookmap-like smoothing
- **Heatmap Intensity**: Try 1.5-2.0 for good visibility
- **Transparency**: Set to 0.7 for nice blending
- **Show Volume Numbers**: Enable to see actual volume values

**Colors:**
- **Bid Color**: Green (default)
- **Ask Color**: Red (default)
- **Background**: Black (default)

## Troubleshooting

### If Compilation Fails:
- Make sure the file is in the correct `ACS_Source` folder
- Check that SierraChart is the latest version
- Verify no other studies with the same name exist

### If Study Doesn't Appear:
- Check that your data feed provides market depth data
- Ensure the symbol supports DOM (Depth of Market)
- Try restarting SierraChart after compilation

### If Heatmap Isn't Visible:
- Increase **Heatmap Intensity** to 2.0 or higher
- Check **Transparency** isn't set too low (should be 0.5 or higher)
- Verify **Min Volume Threshold** isn't set too high
- Make sure **Show Bid Side** and **Show Ask Side** are enabled

## Quick Settings for Bookmap Look:
- Smoothing Period: 15
- Heatmap Intensity: 2.0
- Transparency: 0.7
- Show Volume Numbers: Yes
- Update Frequency: 100ms

The study should now display a real-time depth of market heatmap with smooth, Bookmap-style visualization!
