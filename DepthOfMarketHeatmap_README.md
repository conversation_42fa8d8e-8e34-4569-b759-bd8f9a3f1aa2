# Depth of Market Heatmap Study for SierraChart

This SierraChart study creates a depth of market heatmap visualization similar to Bookmap, with advanced smoothing options to create a more polished and professional appearance.

## Features

### Core Functionality
- **Real-time Market Depth Visualization**: Displays bid and ask depth data as a color-coded heatmap
- **Bookmap-style Appearance**: Mimics the popular Bookmap visualization with smooth color gradients
- **Smoothing Options**: Configurable exponential smoothing to reduce noise and create smoother visuals

### Visual Features
- **Dual-sided Display**: Shows bid depth on the left (green) and ask depth on the right (red)
- **Color Intensity Mapping**: Volume intensity determines color brightness
- **Transparency Control**: Adjustable transparency for better chart integration
- **Volume Numbers**: Optional display of actual volume numbers on the heatmap
- **Customizable Colors**: Fully configurable bid, ask, and background colors

### Advanced Settings
- **Smoothing Period**: Exponential moving average smoothing (1-100 periods)
- **Depth Levels**: Number of price levels to display above/below current price
- **Price Range**: Configurable tick range around current price
- **Volume Threshold**: Minimum volume required to display on heatmap
- **Update Frequency**: Configurable refresh rate (50-1000ms)
- **Heatmap Intensity**: Multiplier for color intensity (0.1-5.0x)

## Installation

1. Copy `DepthOfMarketHeatmap.cpp` to your SierraChart `ACS_Source` folder
2. Compile the study using SierraChart's build system:
   - Go to Analysis → Build Advanced Custom Study DLL
   - Select the file and build
3. Add the study to your chart:
   - Analysis → Studies → Add Custom Study
   - Select "Depth of Market Heatmap"

## Configuration

### Essential Settings

**Smoothing Period** (Default: 10)
- Controls how much smoothing is applied to the depth data
- 1 = No smoothing (raw data)
- 10-20 = Moderate smoothing (recommended for most use cases)
- 50+ = Heavy smoothing (very smooth, Bookmap-like appearance)

**Depth Levels** (Default: 20)
- Number of price levels above and below current price to display
- Higher values show more market depth but may impact performance

**Price Range** (Default: 50 ticks)
- Total price range to scan for depth data
- Should be larger than Depth Levels for best results

### Visual Customization

**Heatmap Intensity** (Default: 1.0)
- Multiplier for color brightness
- Values > 1.0 make colors more vivid
- Values < 1.0 make colors more subtle

**Transparency** (Default: 0.7)
- Controls overall transparency of the heatmap
- 0.1 = Very transparent
- 1.0 = Completely opaque

**Colors**
- Bid Color: Default green (RGB 0,255,0)
- Ask Color: Default red (RGB 255,0,0)
- Background: Default black (RGB 0,0,0)

### Performance Settings

**Update Frequency** (Default: 100ms)
- How often the heatmap refreshes
- Lower values = more responsive but higher CPU usage
- Higher values = less CPU usage but less responsive

**Min Volume Threshold** (Default: 10)
- Minimum volume required to display a price level
- Helps filter out noise and improve performance

## Usage Tips

### For Bookmap-like Appearance
1. Set Smoothing Period to 15-25
2. Set Heatmap Intensity to 1.5-2.0
3. Set Transparency to 0.6-0.8
4. Enable Volume Numbers
5. Use default green/red colors

### For High-Frequency Trading
1. Set Update Frequency to 50ms
2. Set Smoothing Period to 5-10
3. Increase Min Volume Threshold to filter noise
4. Disable Volume Numbers for cleaner display

### For Swing Trading
1. Set Smoothing Period to 30-50
2. Set Update Frequency to 500ms
3. Increase Depth Levels to 30-40
4. Enable Volume Accumulation

## Requirements

- SierraChart with market depth data feed
- Real-time or historical market depth data
- Sufficient CPU for real-time processing

## Troubleshooting

**"Market depth data not available" message**
- Ensure your data feed provides market depth data
- Check that the symbol supports depth of market
- Verify UsesMarketDepthData is enabled in study settings

**Poor Performance**
- Increase Update Frequency (higher ms value)
- Reduce Depth Levels
- Increase Min Volume Threshold
- Reduce Price Range

**Heatmap not visible**
- Check Transparency setting (should be > 0.1)
- Verify Heatmap Intensity is > 0
- Ensure Min Volume Threshold isn't too high
- Check that bid/ask colors aren't same as background

## Technical Notes

- Uses SierraChart's c_ACSILDepthBars interface for depth data access
- Implements exponential moving average smoothing for noise reduction
- Utilizes SierraChart's graphics API for efficient rendering
- Designed for minimal memory footprint and optimal performance

## Version History

- v1.0: Initial release with core heatmap functionality
- Features: Basic depth visualization, smoothing, color customization
