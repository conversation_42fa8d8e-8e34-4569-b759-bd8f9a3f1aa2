#include "sierrachart.h"

SCDLLName("Market Depth Historical Graph with Smoothing")

/*==========================================================================*/
SCSFExport scsf_MarketDepthHistoricalGraph(SCStudyInterfaceRef sc)
{
	// Subgraphs for bid and ask depth visualization
	SCSubgraphRef BidDepth = sc.Subgraph[0];
	SCSubgraphRef AskDepth = sc.Subgraph[1];
	SCSubgraphRef BidDepthSmoothed = sc.Subgraph[2];
	SCSubgraphRef AskDepthSmoothed = sc.Subgraph[3];
	SCSubgraphRef BidDepthGradient = sc.Subgraph[4];
	SCSubgraphRef AskDepthGradient = sc.Subgraph[5];
	SCSubgraphRef CombinedDepth = sc.Subgraph[6];
	SCSubgraphRef DepthImbalance = sc.Subgraph[7];
	
	// Input settings
	SCInputRef SmoothingLength = sc.Input[0];
	SCInputRef SmoothingType = sc.Input[1];
	SCInputRef MaxDepthLevels = sc.Input[2];
	SCInputRef DepthRange = sc.Input[3];
	SCInputRef VolumeMultiplier = sc.Input[4];
	SCInputRef ShowRawData = sc.Input[5];
	SCInputRef ShowSmoothedData = sc.Input[6];
	SCInputRef BidColor = sc.Input[7];
	SCInputRef AskColor = sc.Input[8];
	SCInputRef Transparency = sc.Input[9];
	SCInputRef UseGradientColors = sc.Input[10];
	SCInputRef ShowImbalance = sc.Input[11];
	SCInputRef ImbalanceThreshold = sc.Input[12];
	SCInputRef BookmapStyle = sc.Input[13];
	SCInputRef AdaptiveSmoothing = sc.Input[14];

	if (sc.SetDefaults)
	{
		sc.GraphName = "Market Depth Historical Graph";
		sc.StudyDescription = "Displays historical market depth data with Bookmap-style smoothing";
		
		sc.GraphRegion = 0;
		sc.ValueFormat = 0;
		sc.AutoLoop = 1;
		sc.ScaleRangeType = SCALE_INDEPENDENT;
		
		// Subgraph settings
		BidDepth.Name = "Bid Depth";
		BidDepth.DrawStyle = DRAWSTYLE_FILL_RECTANGLE_TOP;
		BidDepth.PrimaryColor = RGB(0, 255, 0);
		BidDepth.SecondaryColor = RGB(0, 200, 0);
		BidDepth.DrawZeros = false;
		
		AskDepth.Name = "Ask Depth";
		AskDepth.DrawStyle = DRAWSTYLE_FILL_RECTANGLE_BOTTOM;
		AskDepth.PrimaryColor = RGB(255, 0, 0);
		AskDepth.SecondaryColor = RGB(200, 0, 0);
		AskDepth.DrawZeros = false;
		
		BidDepthSmoothed.Name = "Bid Depth Smoothed";
		BidDepthSmoothed.DrawStyle = DRAWSTYLE_FILL_RECTANGLE_TOP;
		BidDepthSmoothed.PrimaryColor = RGB(0, 180, 0);
		BidDepthSmoothed.SecondaryColor = RGB(0, 150, 0);
		BidDepthSmoothed.DrawZeros = false;
		
		AskDepthSmoothed.Name = "Ask Depth Smoothed";
		AskDepthSmoothed.DrawStyle = DRAWSTYLE_FILL_RECTANGLE_BOTTOM;
		AskDepthSmoothed.PrimaryColor = RGB(180, 0, 0);
		AskDepthSmoothed.SecondaryColor = RGB(150, 0, 0);
		AskDepthSmoothed.DrawZeros = false;

		BidDepthGradient.Name = "Bid Depth Gradient";
		BidDepthGradient.DrawStyle = DRAWSTYLE_FILL_RECTANGLE_TOP;
		BidDepthGradient.PrimaryColor = RGB(0, 255, 0);
		BidDepthGradient.DrawZeros = false;

		AskDepthGradient.Name = "Ask Depth Gradient";
		AskDepthGradient.DrawStyle = DRAWSTYLE_FILL_RECTANGLE_BOTTOM;
		AskDepthGradient.PrimaryColor = RGB(255, 0, 0);
		AskDepthGradient.DrawZeros = false;

		CombinedDepth.Name = "Combined Depth";
		CombinedDepth.DrawStyle = DRAWSTYLE_LINE;
		CombinedDepth.PrimaryColor = RGB(255, 255, 0);
		CombinedDepth.DrawZeros = false;

		DepthImbalance.Name = "Depth Imbalance";
		DepthImbalance.DrawStyle = DRAWSTYLE_BAR;
		DepthImbalance.PrimaryColor = RGB(255, 255, 255);
		DepthImbalance.DrawZeros = false;
		
		// Input settings
		SmoothingLength.Name = "Smoothing Length";
		SmoothingLength.SetInt(5);
		SmoothingLength.SetIntLimits(1, 50);
		SmoothingLength.SetDescription("Number of bars to use for smoothing (higher = smoother like Bookmap)");
		
		SmoothingType.Name = "Smoothing Type";
		SmoothingType.SetMovAvgType(MOVAVGTYPE_EXPONENTIAL);
		SmoothingType.SetDescription("Type of smoothing to apply");
		
		MaxDepthLevels.Name = "Max Depth Levels";
		MaxDepthLevels.SetInt(10);
		MaxDepthLevels.SetIntLimits(1, 50);
		MaxDepthLevels.SetDescription("Maximum number of depth levels to display");
		
		DepthRange.Name = "Depth Range (Ticks)";
		DepthRange.SetInt(20);
		DepthRange.SetIntLimits(5, 100);
		DepthRange.SetDescription("Price range in ticks to display depth data");
		
		VolumeMultiplier.Name = "Volume Multiplier";
		VolumeMultiplier.SetFloat(1.0f);
		VolumeMultiplier.SetFloatLimits(0.1f, 10.0f);
		VolumeMultiplier.SetDescription("Multiplier for volume display intensity");
		
		ShowRawData.Name = "Show Raw Data";
		ShowRawData.SetYesNo(false);
		ShowRawData.SetDescription("Show unsmoothed depth data");
		
		ShowSmoothedData.Name = "Show Smoothed Data";
		ShowSmoothedData.SetYesNo(true);
		ShowSmoothedData.SetDescription("Show smoothed depth data (Bookmap style)");
		
		BidColor.Name = "Bid Color";
		BidColor.SetColor(RGB(0, 255, 0));
		BidColor.SetDescription("Color for bid depth");
		
		AskColor.Name = "Ask Color";
		AskColor.SetColor(RGB(255, 0, 0));
		AskColor.SetDescription("Color for ask depth");
		
		Transparency.Name = "Transparency %";
		Transparency.SetInt(30);
		Transparency.SetIntLimits(0, 90);
		Transparency.SetDescription("Transparency percentage for depth visualization");

		UseGradientColors.Name = "Use Gradient Colors";
		UseGradientColors.SetYesNo(true);
		UseGradientColors.SetDescription("Use gradient colors based on depth intensity (Bookmap style)");

		ShowImbalance.Name = "Show Imbalance";
		ShowImbalance.SetYesNo(false);
		ShowImbalance.SetDescription("Show bid/ask imbalance indicator");

		ImbalanceThreshold.Name = "Imbalance Threshold %";
		ImbalanceThreshold.SetFloat(20.0f);
		ImbalanceThreshold.SetFloatLimits(5.0f, 80.0f);
		ImbalanceThreshold.SetDescription("Threshold for significant imbalance detection");

		BookmapStyle.Name = "Bookmap Style Mode";
		BookmapStyle.SetYesNo(true);
		BookmapStyle.SetDescription("Enable Bookmap-like visualization style");

		AdaptiveSmoothing.Name = "Adaptive Smoothing";
		AdaptiveSmoothing.SetYesNo(false);
		AdaptiveSmoothing.SetDescription("Use adaptive smoothing based on market volatility");

		return;
	}
	
	// Get market depth data
	c_ACSILDepthBars* DepthBars = sc.GetMarketDepthBars();
	if (DepthBars == nullptr)
	{
		sc.AddMessageToLog("Market depth data not available", 1);
		return;
	}
	
	// Check if depth data exists for current bar
	if (!DepthBars->DepthDataExistsAt(sc.Index))
		return;
	
	// Get current bar price information
	float CurrentPrice = sc.Close[sc.Index];
	float TickSize = DepthBars->GetTickSize();
	int CurrentPriceTickIndex = DepthBars->PriceToTickIndex(CurrentPrice);
	
	// Calculate depth range
	int DepthRangeTicks = DepthRange.GetInt();
	int MaxLevels = MaxDepthLevels.GetInt();
	float VolMultiplier = VolumeMultiplier.GetFloat();
	bool UseBookmapStyle = BookmapStyle.GetYesNo();
	bool UseAdaptiveSmoothing = AdaptiveSmoothing.GetYesNo();

	// Initialize depth values
	float TotalBidDepth = 0.0f;
	float TotalAskDepth = 0.0f;
	float WeightedBidDepth = 0.0f;
	float WeightedAskDepth = 0.0f;
	float MaxBidAtLevel = 0.0f;
	float MaxAskAtLevel = 0.0f;

	// Calculate adaptive smoothing factor based on volatility
	float AdaptiveFactor = 1.0f;
	if (UseAdaptiveSmoothing && sc.Index > 20)
	{
		float VolatilitySum = 0.0f;
		for (int i = 1; i <= 20; i++)
		{
			float PriceChange = abs(sc.Close[sc.Index - i] - sc.Close[sc.Index - i - 1]);
			VolatilitySum += PriceChange / TickSize;
		}
		float AvgVolatility = VolatilitySum / 20.0f;
		AdaptiveFactor = 1.0f + (AvgVolatility * 0.1f); // Increase smoothing with volatility
	}

	// Bookmap-style depth aggregation with exponential distance weighting
	for (int TickOffset = -DepthRangeTicks; TickOffset <= DepthRangeTicks; TickOffset++)
	{
		int PriceTickIndex = CurrentPriceTickIndex + TickOffset;

		// Get bid and ask quantities at this price level
		int BidQuantity = DepthBars->GetMaxBidQuantity(sc.Index, PriceTickIndex);
		int AskQuantity = DepthBars->GetMaxAskQuantity(sc.Index, PriceTickIndex);

		if (BidQuantity > 0 || AskQuantity > 0)
		{
			// Bookmap-style exponential distance weighting
			float DistanceWeight = UseBookmapStyle ?
				exp(-abs(TickOffset) * 0.15f) : // Exponential decay for Bookmap style
				1.0f / (1.0f + abs(TickOffset) * 0.1f); // Linear decay for traditional style

			// Apply volume multiplier and distance weighting
			float WeightedBid = BidQuantity * DistanceWeight * VolMultiplier;
			float WeightedAsk = AskQuantity * DistanceWeight * VolMultiplier;

			TotalBidDepth += WeightedBid;
			TotalAskDepth += WeightedAsk;

			// Track maximum depth at any level for gradient calculation
			if (WeightedBid > MaxBidAtLevel) MaxBidAtLevel = WeightedBid;
			if (WeightedAsk > MaxAskAtLevel) MaxAskAtLevel = WeightedAsk;
		}
	}
	
	// Store raw depth data
	BidDepth[sc.Index] = TotalBidDepth;
	AskDepth[sc.Index] = -TotalAskDepth; // Negative for bottom display

	// Calculate combined depth and imbalance
	CombinedDepth[sc.Index] = TotalBidDepth + TotalAskDepth;

	// Calculate depth imbalance (positive = bid heavy, negative = ask heavy)
	float TotalDepth = TotalBidDepth + TotalAskDepth;
	float ImbalanceRatio = 0.0f;
	if (TotalDepth > 0)
	{
		ImbalanceRatio = ((TotalBidDepth - TotalAskDepth) / TotalDepth) * 100.0f;
	}
	DepthImbalance[sc.Index] = ImbalanceRatio;

	// Apply smoothing for Bookmap-like appearance
	if (ShowSmoothedData.GetYesNo())
	{
		int EffectiveSmoothingLength = SmoothingLength.GetInt();
		if (UseAdaptiveSmoothing)
		{
			EffectiveSmoothingLength = max(1, (int)(SmoothingLength.GetInt() * AdaptiveFactor));
		}

		// Apply smoothing based on selected type
		switch (SmoothingType.GetMovAvgType())
		{
			case MOVAVGTYPE_SIMPLE:
				sc.SimpleMovAvg(BidDepth, BidDepthSmoothed, EffectiveSmoothingLength);
				sc.SimpleMovAvg(AskDepth, AskDepthSmoothed, EffectiveSmoothingLength);
				break;

			case MOVAVGTYPE_EXPONENTIAL:
				sc.ExponentialMovAvg(BidDepth, BidDepthSmoothed, EffectiveSmoothingLength);
				sc.ExponentialMovAvg(AskDepth, AskDepthSmoothed, EffectiveSmoothingLength);
				break;

			case MOVAVGTYPE_WEIGHTED:
				sc.WeightedMovingAverage(BidDepth, BidDepthSmoothed, EffectiveSmoothingLength);
				sc.WeightedMovingAverage(AskDepth, AskDepthSmoothed, EffectiveSmoothingLength);
				break;

			case MOVAVGTYPE_HULL:
				sc.HullMovingAverage(BidDepth, BidDepthSmoothed, EffectiveSmoothingLength);
				sc.HullMovingAverage(AskDepth, AskDepthSmoothed, EffectiveSmoothingLength);
				break;

			default:
				sc.ExponentialMovAvg(BidDepth, BidDepthSmoothed, EffectiveSmoothingLength);
				sc.ExponentialMovAvg(AskDepth, AskDepthSmoothed, EffectiveSmoothingLength);
				break;
		}

		// Apply additional Bookmap-style smoothing if enabled
		if (UseBookmapStyle)
		{
			// Secondary smoothing pass for ultra-smooth Bookmap appearance
			sc.ExponentialMovAvg(BidDepthSmoothed, BidDepthGradient, max(1, EffectiveSmoothingLength / 2));
			sc.ExponentialMovAvg(AskDepthSmoothed, AskDepthGradient, max(1, EffectiveSmoothingLength / 2));
		}
	}
	
	// Update colors based on user settings and gradient intensity
	COLORREF BidColorValue = BidColor.GetColor();
	COLORREF AskColorValue = AskColor.GetColor();
	int TransparencyValue = Transparency.GetInt();
	bool UseGradient = UseGradientColors.GetYesNo();
	bool ShowImbal = ShowImbalance.GetYesNo();

	// Calculate gradient intensity based on depth levels
	float BidIntensity = MaxBidAtLevel > 0 ? min(1.0f, TotalBidDepth / MaxBidAtLevel) : 0.0f;
	float AskIntensity = MaxAskAtLevel > 0 ? min(1.0f, TotalAskDepth / MaxAskAtLevel) : 0.0f;

	// Apply gradient coloring if enabled (Bookmap style)
	if (UseGradient)
	{
		// Enhance color intensity based on depth
		int BidR = GetRValue(BidColorValue);
		int BidG = GetGValue(BidColorValue);
		int BidB = GetBValue(BidColorValue);

		int AskR = GetRValue(AskColorValue);
		int AskG = GetGValue(AskColorValue);
		int AskB = GetBValue(AskColorValue);

		// Apply intensity gradient
		BidColorValue = RGB((int)(BidR * (0.3f + 0.7f * BidIntensity)),
							(int)(BidG * (0.3f + 0.7f * BidIntensity)),
							(int)(BidB * (0.3f + 0.7f * BidIntensity)));

		AskColorValue = RGB((int)(AskR * (0.3f + 0.7f * AskIntensity)),
							(int)(AskG * (0.3f + 0.7f * AskIntensity)),
							(int)(AskB * (0.3f + 0.7f * AskIntensity)));
	}

	// Apply transparency
	BidColorValue = RGB(GetRValue(BidColorValue) * (100 - TransparencyValue) / 100,
						GetGValue(BidColorValue) * (100 - TransparencyValue) / 100,
						GetBValue(BidColorValue) * (100 - TransparencyValue) / 100);

	AskColorValue = RGB(GetRValue(AskColorValue) * (100 - TransparencyValue) / 100,
						GetGValue(AskColorValue) * (100 - TransparencyValue) / 100,
						GetBValue(AskColorValue) * (100 - TransparencyValue) / 100);

	// Set subgraph visibility and colors based on mode
	BidDepth.DrawStyle = ShowRawData.GetYesNo() ? DRAWSTYLE_FILL_RECTANGLE_TOP : DRAWSTYLE_IGNORE;
	AskDepth.DrawStyle = ShowRawData.GetYesNo() ? DRAWSTYLE_FILL_RECTANGLE_BOTTOM : DRAWSTYLE_IGNORE;

	if (UseBookmapStyle && ShowSmoothedData.GetYesNo())
	{
		// Use gradient subgraphs for Bookmap style
		BidDepthGradient.DrawStyle = DRAWSTYLE_FILL_RECTANGLE_TOP;
		AskDepthGradient.DrawStyle = DRAWSTYLE_FILL_RECTANGLE_BOTTOM;
		BidDepthSmoothed.DrawStyle = DRAWSTYLE_IGNORE;
		AskDepthSmoothed.DrawStyle = DRAWSTYLE_IGNORE;

		BidDepthGradient.PrimaryColor = BidColorValue;
		AskDepthGradient.PrimaryColor = AskColorValue;
	}
	else
	{
		// Use standard smoothed subgraphs
		BidDepthSmoothed.DrawStyle = ShowSmoothedData.GetYesNo() ? DRAWSTYLE_FILL_RECTANGLE_TOP : DRAWSTYLE_IGNORE;
		AskDepthSmoothed.DrawStyle = ShowSmoothedData.GetYesNo() ? DRAWSTYLE_FILL_RECTANGLE_BOTTOM : DRAWSTYLE_IGNORE;
		BidDepthGradient.DrawStyle = DRAWSTYLE_IGNORE;
		AskDepthGradient.DrawStyle = DRAWSTYLE_IGNORE;

		if (ShowSmoothedData.GetYesNo())
		{
			BidDepthSmoothed.PrimaryColor = BidColorValue;
			AskDepthSmoothed.PrimaryColor = AskColorValue;
		}
	}

	// Set imbalance display
	DepthImbalance.DrawStyle = ShowImbal ? DRAWSTYLE_BAR : DRAWSTYLE_IGNORE;

	// Color imbalance based on direction and threshold
	if (ShowImbal)
	{
		float ImbalanceThresh = ImbalanceThreshold.GetFloat();
		if (abs(ImbalanceRatio) > ImbalanceThresh)
		{
			DepthImbalance.PrimaryColor = ImbalanceRatio > 0 ? RGB(0, 255, 0) : RGB(255, 0, 0);
		}
		else
		{
			DepthImbalance.PrimaryColor = RGB(128, 128, 128);
		}
	}

	if (ShowRawData.GetYesNo())
	{
		BidDepth.PrimaryColor = BidColorValue;
		AskDepth.PrimaryColor = AskColorValue;
	}
}
